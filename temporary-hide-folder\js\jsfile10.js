// ========================================
// EXPORT AND CANVAS MANAGEMENT
// ========================================

// Extend CF7TextEditor class with export and canvas management methods
Object.assign(CF7TextEditor.prototype, {
    clearCanvas() {
            if (confirm('Are you sure you want to clear all elements?')) {
                this.elementsContainer.innerHTML = '';
                this.selectedElement = null;
                this.elementCounter = 0;
            }
        },

        // CF7 Export Functionality - Configurable High Quality Export with Visual Fidelity
        async exportCanvasAsPNG() {
            try {
                // Get export configuration from UI
                const qualitySelect = document.getElementById('cf7-export-quality');
                const formatSelect = document.getElementById('cf7-export-format');

                const quality = qualitySelect ? qualitySelect.value : 'standard';
                const format = formatSelect ? formatSelect.value : 'png';

                // Show loading indicator
                this.showExportProgress('Preparing export...');

                // Handle background image embedding first
                await this.prepareBackgroundForExport();

                // Temporarily hide selection indicators and resize handles for clean export
                const selectedElements = this.canvas.querySelectorAll('.cf7-selected');
                const resizeHandles = this.canvas.querySelectorAll('.cf7-resize-handle');
                const deleteButtons = this.canvas.querySelectorAll('.cf7-delete-btn');
                const editingElements = this.canvas.querySelectorAll('.cf7-editing');

                // Store original states and clean up all visual editing indicators
                const originalStates = [];
                const allDraggableElements = this.canvas.querySelectorAll('.cf7-draggable-text, .cf7-draggable-image');

                // Store and clean up all draggable elements
                [...selectedElements, ...resizeHandles, ...deleteButtons, ...editingElements, ...allDraggableElements].forEach(el => {
                    originalStates.push({
                        element: el,
                        className: el.className,
                        display: el.style.display,
                        border: el.style.border,
                        background: el.style.background,
                        cursor: el.style.cursor,
                        outline: el.style.outline,
                        boxShadow: el.style.boxShadow
                    });
                });

                // Remove all editing and selection states
                [...selectedElements, ...editingElements].forEach(el => {
                    el.classList.remove('cf7-selected', 'cf7-editing');
                });

                // Hide UI control elements
                [...resizeHandles, ...deleteButtons].forEach(el => {
                    el.style.display = 'none';
                });

                // Clean up ALL visual editing indicators from draggable elements
                allDraggableElements.forEach(el => {
                    // Remove all editor-related classes temporarily
                    el.classList.remove('cf7-selected', 'cf7-editing');

                    // Clear all visual editing styles
                    el.style.border = 'none';
                    el.style.background = 'transparent';
                    el.style.cursor = 'default';
                    el.style.outline = 'none';
                    el.style.boxShadow = 'none';

                    // Remove hover effects by adding a temporary class
                    el.classList.add('cf7-export-mode');
                });

                // Get canvas dimensions for proper scaling
                const canvasRect = this.canvas.getBoundingClientRect();
                const canvasWidth = parseInt(this.canvas.dataset.width) || 800;
                const canvasHeight = parseInt(this.canvas.dataset.height) || 400;

                this.showExportProgress('Capturing image...');

                // Configure quality settings
                let pixelRatio, canvasMultiplier, jpegQuality;
                switch (quality) {
                    case 'high':
                        pixelRatio = 3;
                        canvasMultiplier = 3;
                        jpegQuality = 0.95;
                        break;
                    case 'web':
                        pixelRatio = 1;
                        canvasMultiplier = 1;
                        jpegQuality = 0.8;
                        break;
                    default: // standard
                        pixelRatio = 2;
                        canvasMultiplier = 2;
                        jpegQuality = 0.9;
                }

                // Configure export options for maximum visual fidelity
                const exportOptions = {
                    quality: jpegQuality,
                    pixelRatio: pixelRatio,
                    backgroundColor: format === 'png' ? 'transparent' : '#ffffff',
                    cacheBust: true,
                    width: canvasWidth,
                    height: canvasHeight,
                    canvasWidth: canvasWidth * canvasMultiplier,
                    canvasHeight: canvasHeight * canvasMultiplier,
                    style: {
                        // Preserve all visual styles exactly as they appear
                        transform: 'scale(1)',
                        transformOrigin: 'top left',
                        width: canvasWidth + 'px',
                        height: canvasHeight + 'px'
                    },
                    filter: (node) => {
                        // Exclude UI elements that shouldn't be in the export
                        if (node.classList) {
                            return !node.classList.contains('cf7-delete-btn') &&
                                   !node.classList.contains('cf7-resize-handle') &&
                                   !node.classList.contains('cf7-selected') &&
                                   !node.classList.contains('cf7-editing') &&
                                   !node.hasAttribute('data-cf7-ui-element');
                        }
                        return true;
                    },
                    // Handle CORS issues gracefully
                    fontEmbedCSS: null,
                    includeQueryParams: false,
                    skipAutoScale: false,
                    // Use a placeholder for failed images instead of breaking
                    imagePlaceholder: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkJhY2tncm91bmQgSW1hZ2U8L3RleHQ+PC9zdmc+',
                    // Don't use CORS for external images to avoid blocking
                    useCORS: false
                };

                // Export based on selected format
                let dataUrl;
                if (format === 'jpeg') {
                    dataUrl = await htmlToImage.toJpeg(this.canvas, exportOptions);
                } else {
                    dataUrl = await htmlToImage.toPng(this.canvas, exportOptions);
                }

                this.showExportProgress('Downloading...');

                // Create download link with descriptive filename
                const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                const qualityLabel = quality.charAt(0).toUpperCase() + quality.slice(1);
                const link = document.createElement('a');
                link.download = `cf7-billboard-${qualityLabel}-${timestamp}.${format}`;
                link.href = dataUrl;

                // Trigger download
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Restore original states
                originalStates.forEach(state => {
                    state.element.className = state.className;
                    state.element.style.display = state.display;
                    state.element.style.border = state.border;
                    state.element.style.background = state.background;
                    state.element.style.cursor = state.cursor;
                    state.element.style.outline = state.outline;
                    state.element.style.boxShadow = state.boxShadow;
                });

                // Remove export mode class from all elements
                const exportModeElements = this.canvas.querySelectorAll('.cf7-export-mode');
                exportModeElements.forEach(el => {
                    el.classList.remove('cf7-export-mode');
                });

                // Restore background after export
                await this.restoreBackgroundAfterExport();

                this.hideExportProgress();

                // Check if we have a background image and show appropriate message
                const backgroundElement = document.getElementById('cf7-canvas-bg');
                const hasBackgroundImage = backgroundElement &&
                    backgroundElement.style.backgroundImage &&
                    backgroundElement.style.backgroundImage !== 'none';

                if (hasBackgroundImage) {
                    this.showExportWarning(`${format.toUpperCase()} exported (${qualityLabel} quality). Note: Background images from external sources may not appear due to browser security restrictions. For best results, use local images or serve from the same domain.`);
                } else {
                    this.showExportSuccess(`${format.toUpperCase()} exported successfully (${qualityLabel} quality)`);
                }

                console.log(`CF7 Export: ${format.toUpperCase()} exported successfully with ${quality} quality`);

            } catch (error) {
                console.error('CF7 Export Error:', error);
                this.hideExportProgress();
                this.showExportError(error.message);

                // Restore states in case of error
                if (originalStates && originalStates.length > 0) {
                    originalStates.forEach(state => {
                        state.element.className = state.className;
                        state.element.style.display = state.display;
                        state.element.style.border = state.border;
                        state.element.style.background = state.background;
                        state.element.style.cursor = state.cursor;
                        state.element.style.outline = state.outline;
                        state.element.style.boxShadow = state.boxShadow;
                    });
                } else {
                    // Fallback restoration
                    const allElements = this.canvas.querySelectorAll('*');
                    allElements.forEach(el => {
                        el.style.display = '';
                        el.style.border = '';
                        el.style.background = '';
                        el.style.cursor = '';
                        el.style.outline = '';
                        el.style.boxShadow = '';
                        el.classList.remove('cf7-export-mode');
                    });
                }

                // Remove export mode class from all elements
                const exportModeElements = this.canvas.querySelectorAll('.cf7-export-mode');
                exportModeElements.forEach(el => {
                    el.classList.remove('cf7-export-mode');
                });

                // Restore background in case of error
                await this.restoreBackgroundAfterExport();
            }
        },

        // Export progress and feedback methods
        showExportProgress(message) {
            // Remove existing progress indicator
            this.hideExportProgress();

            const progressDiv = document.createElement('div');
            progressDiv.id = 'cf7-export-progress';
            progressDiv.className = 'cf7-export-progress-message';
            progressDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #007cba, #0056b3);
                color: white;
                padding: 20px 25px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 124, 186, 0.3);
                z-index: 999999 !important;
                font-family: Arial, sans-serif;
                font-size: 14px;
                font-weight: 600;
                animation: slideInRight 0.5s ease-out;
                max-width: 300px;
            `;
            progressDiv.innerHTML = `
                <div style="display: flex; align-items: center; gap: 15px;">
                    <div style="width: 24px; height: 24px; border: 3px solid rgba(255,255,255,0.3); border-top: 3px solid white; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                    <div>
                        <div style="font-weight: 700; margin-bottom: 2px;">Processing...</div>
                        <div style="font-size: 12px; opacity: 0.9;">${message}</div>
                    </div>
                </div>
                <style>
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                </style>
            `;

            // Ensure animation keyframes are available
            if (!document.querySelector('#cf7-chip-animations')) {
                const style = document.createElement('style');
                style.id = 'cf7-chip-animations';
                style.textContent = `
                    @keyframes slideInRight {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    @keyframes fadeOut {
                        from { opacity: 1; }
                        to { opacity: 0; }
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(progressDiv);
        },

        hideExportProgress() {
            const progressDiv = document.getElementById('cf7-export-progress');
            if (progressDiv) {
                progressDiv.remove();
            }
        },

        showExportSuccess(message) {
            // Remove any existing success messages
            const existingMessages = document.querySelectorAll('.cf7-export-success-message');
            existingMessages.forEach(msg => msg.remove());

            const successDiv = document.createElement('div');
            successDiv.className = 'cf7-export-success-message';
            successDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                padding: 15px 25px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
                z-index: 999999 !important;
                font-family: Arial, sans-serif;
                font-size: 14px;
                font-weight: 600;
                animation: slideInRight 0.5s ease-out;
                max-width: 300px;
            `;

            successDiv.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span style="font-size: 18px;">✅</span>
                    <div>
                        <div style="font-weight: 700; margin-bottom: 2px;">Export Success!</div>
                        <div style="font-size: 12px; opacity: 0.9;">${message || 'Image exported successfully!'}</div>
                    </div>
                </div>
            `;

            // Add animation keyframes if not already added
            if (!document.querySelector('#cf7-chip-animations')) {
                const style = document.createElement('style');
                style.id = 'cf7-chip-animations';
                style.textContent = `
                    @keyframes slideInRight {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    @keyframes fadeOut {
                        from { opacity: 1; }
                        to { opacity: 0; }
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(successDiv);

            // Auto-remove after 4 seconds with fade out
            setTimeout(() => {
                successDiv.style.animation = 'fadeOut 0.5s ease-out';
                setTimeout(() => {
                    if (successDiv.parentNode) {
                        successDiv.remove();
                    }
                }, 500);
            }, 4000);
        },

        showExportWarning(message) {
            // Remove any existing warning messages
            const existingMessages = document.querySelectorAll('.cf7-export-warning-message');
            existingMessages.forEach(msg => msg.remove());

            const warningDiv = document.createElement('div');
            warningDiv.className = 'cf7-export-warning-message';
            warningDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #ffc107, #ffca2c);
                color: #212529;
                padding: 15px 25px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
                z-index: 999999 !important;
                font-family: Arial, sans-serif;
                font-size: 14px;
                font-weight: 600;
                animation: slideInRight 0.5s ease-out;
                max-width: 300px;
            `;

            warningDiv.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span style="font-size: 18px;">⚠️</span>
                    <div>
                        <div style="font-weight: 700; margin-bottom: 2px;">Export Notice</div>
                        <div style="font-size: 12px; opacity: 0.9;">${message}</div>
                    </div>
                </div>
            `;

            // Ensure animation keyframes are available
            if (!document.querySelector('#cf7-chip-animations')) {
                const style = document.createElement('style');
                style.id = 'cf7-chip-animations';
                style.textContent = `
                    @keyframes slideInRight {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    @keyframes fadeOut {
                        from { opacity: 1; }
                        to { opacity: 0; }
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(warningDiv);

            // Auto-remove after 6 seconds with fade out
            setTimeout(() => {
                warningDiv.style.animation = 'fadeOut 0.5s ease-out';
                setTimeout(() => {
                    if (warningDiv.parentNode) {
                        warningDiv.remove();
                    }
                }, 500);
            }, 6000);
        },

        showExportError(errorMessage) {
            // Remove any existing error messages
            const existingMessages = document.querySelectorAll('.cf7-export-error-message');
            existingMessages.forEach(msg => msg.remove());

            const errorDiv = document.createElement('div');
            errorDiv.className = 'cf7-export-error-message';
            errorDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #dc3545, #c82333);
                color: white;
                padding: 15px 25px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
                z-index: 999999 !important;
                font-family: Arial, sans-serif;
                font-size: 14px;
                font-weight: 600;
                animation: slideInRight 0.5s ease-out;
                max-width: 300px;
            `;

            errorDiv.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span style="font-size: 18px;">❌</span>
                    <div>
                        <div style="font-weight: 700; margin-bottom: 2px;">Export Failed</div>
                        <div style="font-size: 12px; opacity: 0.9;">${errorMessage}</div>
                    </div>
                </div>
            `;

            // Ensure animation keyframes are available
            if (!document.querySelector('#cf7-chip-animations')) {
                const style = document.createElement('style');
                style.id = 'cf7-chip-animations';
                style.textContent = `
                    @keyframes slideInRight {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    @keyframes fadeOut {
                        from { opacity: 1; }
                        to { opacity: 0; }
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(errorDiv);

            // Auto-remove after 8 seconds with fade out
            setTimeout(() => {
                errorDiv.style.animation = 'fadeOut 0.5s ease-out';
                setTimeout(() => {
                    if (errorDiv.parentNode) {
                        errorDiv.remove();
                    }
                }, 500);
            }, 8000);
        },

        // Background image handling for export
        async prepareBackgroundForExport() {
            const backgroundElement = document.getElementById('cf7-canvas-bg');
            if (!backgroundElement) return;

            const currentBackgroundImage = backgroundElement.style.backgroundImage;
            if (!currentBackgroundImage || currentBackgroundImage === 'none') return;

            // Extract URL from CSS background-image property
            const urlMatch = currentBackgroundImage.match(/url\(["']?([^"')]+)["']?\)/);
            if (!urlMatch) return;

            const imageUrl = urlMatch[1];
            console.log('Preparing background image for export:', imageUrl);

            try {
                // Store original background settings
                this.originalBackgroundSettings = {
                    backgroundImage: currentBackgroundImage,
                    backgroundSize: backgroundElement.style.backgroundSize || 'cover',
                    backgroundPosition: backgroundElement.style.backgroundPosition || 'center',
                    backgroundRepeat: backgroundElement.style.backgroundRepeat || 'no-repeat',
                    innerHTML: backgroundElement.innerHTML
                };

                // For CORS-restricted images, use a different approach
                // Create an img element that loads without CORS restrictions
                const img = document.createElement('img');
                img.style.cssText = `
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    object-position: center;
                    z-index: 0;
                    pointer-events: none;
                `;

                // Load image - for external images, this may not work due to CORS
                img.src = imageUrl;

                // Wait for image to load
                await new Promise((resolve, reject) => {
                    img.onload = () => {
                        console.log('Background image loaded successfully for export');
                        resolve();
                    };
                    img.onerror = () => {
                        console.warn('Background image failed to load, using placeholder');
                        // Create a placeholder if image fails to load
                        img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48cGF0dGVybiBpZD0iZ3JpZCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiBwYXR0ZXJuVW5pdHM9InVzZXJTcGFjZU9uVXNlIj48cGF0aCBkPSJNIDIwIDAgTCAwIDAgMCAyMCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZGRkIiBzdHJva2Utd2lkdGg9IjEiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JpZCkiLz48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE4IiBmaWxsPSIjOTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+QmFja2dyb3VuZCBJbWFnZTwvdGV4dD48L3N2Zz4=';
                        resolve();
                    };

                    // Set a timeout to avoid hanging
                    setTimeout(() => {
                        if (!img.complete) {
                            console.warn('Background image load timeout, using placeholder');
                            img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48cGF0dGVybiBpZD0iZ3JpZCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiBwYXR0ZXJuVW5pdHM9InVzZXJTcGFjZU9uVXNlIj48cGF0aCBkPSJNIDIwIDAgTCAwIDAgMCAyMCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZGRkIiBzdHJva2Utd2lkdGg9IjEiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JpZCkiLz48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE4IiBmaWxsPSIjOTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+QmFja2dyb3VuZCBJbWFnZTwvdGV4dD48L3N2Zz4=';
                            resolve();
                        }
                    }, 5000);
                });

                // Clear background image and add img element
                backgroundElement.style.backgroundImage = 'none';
                backgroundElement.appendChild(img);

                console.log('Background image replaced with img element for export');

            } catch (error) {
                console.warn('Failed to prepare background image for export:', error);
                // Continue with original - html-to-image might still capture it
            }
        },

        async restoreBackgroundAfterExport() {
            if (!this.originalBackgroundSettings) return;

            const backgroundElement = document.getElementById('cf7-canvas-bg');
            if (!backgroundElement) return;

            // Remove any img elements that were added for export
            const exportImages = backgroundElement.querySelectorAll('img');
            exportImages.forEach(img => img.remove());

            // Restore original background settings
            backgroundElement.style.backgroundImage = this.originalBackgroundSettings.backgroundImage;
            backgroundElement.style.backgroundSize = this.originalBackgroundSettings.backgroundSize;
            backgroundElement.style.backgroundPosition = this.originalBackgroundSettings.backgroundPosition;
            backgroundElement.style.backgroundRepeat = this.originalBackgroundSettings.backgroundRepeat;
            backgroundElement.innerHTML = this.originalBackgroundSettings.innerHTML;

            // Clear stored settings
            this.originalBackgroundSettings = null;

            console.log('Background image restored after export');
        },

        // Convert image URL to data URL for reliable embedding
        async imageToDataUrl(url) {
            return new Promise((resolve, reject) => {
                const img = new Image();

                // Try with CORS first
                img.crossOrigin = 'anonymous';

                img.onload = function() {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    canvas.width = img.width;
                    canvas.height = img.height;

                    ctx.drawImage(img, 0, 0);

                    try {
                        const dataUrl = canvas.toDataURL('image/png');
                        resolve(dataUrl);
                    } catch (error) {
                        // If canvas is tainted, try without CORS
                        console.warn('Canvas tainted, retrying without CORS');
                        reject(error);
                    }
                };

                img.onerror = function() {
                    // If CORS fails, try without crossOrigin
                    console.warn('CORS failed, retrying without crossOrigin');
                    const img2 = new Image();

                    img2.onload = function() {
                        // For non-CORS images, we can't convert to data URL
                        // but html-to-image might still capture them
                        reject(new Error('CORS not available for image: ' + url));
                    };

                    img2.onerror = function() {
                        reject(new Error('Failed to load image: ' + url));
                    };

                    img2.src = url;
                };

                // Add cache busting parameter
                const separator = url.includes('?') ? '&' : '?';
                img.src = url + separator + '_export=' + Date.now();
            });
        },

        removeBackgroundImage() {
            const backgroundElement = this.container.querySelector('#cf7-canvas-bg');
            backgroundElement.style.backgroundImage = '';
        }
});