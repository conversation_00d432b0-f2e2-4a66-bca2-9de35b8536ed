<?php
// Email configuration for Gmail OTP sending

// Gmail SMTP Configuration - WORKING CREDENTIALS
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'alqinrduvefbdijh');
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', 'Borges Media');

// Email templates
function getOTPEmailTemplate($otp, $adminName) {
    return "
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #28a745 0%, #ff8c00 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .otp-box { background: white; border: 2px solid #28a745; padding: 20px; text-align: center; margin: 20px 0; border-radius: 10px; }
            .otp-code { font-size: 32px; font-weight: bold; color: #28a745; letter-spacing: 5px; }
            .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>Borges Media</h1>
                <p>Admin Login Verification</p>
            </div>
            <div class='content'>
                <h2>Hello $adminName,</h2>
                <p>You have requested to login to the Borges Media admin panel. Please use the following One-Time Password (OTP) to complete your login:</p>
                
                <div class='otp-box'>
                    <p>Your OTP Code:</p>
                    <div class='otp-code'>$otp</div>
                </div>
                
                <p><strong>Important:</strong></p>
                <ul>
                    <li>This OTP is valid for 10 minutes only</li>
                    <li>Do not share this code with anyone</li>
                    <li>If you didn't request this login, please ignore this email</li>
                </ul>
                
                <p>If you have any questions, please contact our support team.</p>
                
                <p>Best regards,<br>Borges Media Team</p>
            </div>
            <div class='footer'>
                <p>&copy; 2025 Borges Media. All rights reserved.</p>
                <p>This is an automated message, please do not reply to this email.</p>
            </div>
        </div>
    </body>
    </html>
    ";
}

// Function to send OTP email
function sendOTPEmail($toEmail, $adminName, $otp) {
    // Check if PHPMailer is available
    if (!class_exists('PHPMailer\PHPMailer\PHPMailer')) {
        // Fallback: Log the OTP instead of sending email
        logSecurityEvent('otp_email_fallback', [
            'email' => $toEmail,
            'otp' => $otp,
            'reason' => 'PHPMailer not available'
        ]);
        return false;
    }

    // Check if Gmail credentials are configured
    if (SMTP_USERNAME === '<EMAIL>' || SMTP_PASSWORD === 'your-app-password') {
        logSecurityEvent('otp_email_not_configured', [
            'email' => $toEmail,
            'reason' => 'Gmail credentials not configured'
        ]);
        return false;
    }
    
    try {
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        
        // Server settings
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = SMTP_USERNAME;
        $mail->Password = SMTP_PASSWORD;
        $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = SMTP_PORT;
        
        // Recipients
        $mail->setFrom(SMTP_FROM_EMAIL, SMTP_FROM_NAME);
        $mail->addAddress($toEmail, $adminName);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = 'Borges Media - Admin Login OTP';
        $mail->Body = getOTPEmailTemplate($otp, $adminName);
        $mail->AltBody = "Your Borges Media admin login OTP is: $otp. This code is valid for 10 minutes.";
        
        $mail->send();
        
        logSecurityEvent('otp_email_sent', [
            'email' => $toEmail,
            'admin_name' => $adminName
        ]);
        
        return true;
        
    } catch (Exception $e) {
        logSecurityEvent('otp_email_failed', [
            'email' => $toEmail,
            'error' => $e->getMessage()
        ]);
        return false;
    }
}

// Function to send email using PHP's built-in mail() function (fallback) - DISABLED
function sendOTPEmailFallback($toEmail, $adminName, $otp) {
    // Disable built-in mail() function as it requires local mail server
    // This prevents the "Failed to connect to mailserver" error

    logSecurityEvent('otp_email_fallback_disabled', [
        'email' => $toEmail,
        'admin_name' => $adminName,
        'reason' => 'Built-in mail() function disabled - requires local mail server'
    ]);

    return false;
}
?>
