<?php
require_once '../config/security.php';
require_once '../config/database.php';

// Try to load PHPMailer if available
if (file_exists('../vendor/autoload.php')) {
    require_once '../vendor/autoload.php';
}
require_once '../config/email.php';

// Redirect if already logged in
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    header('Location: orders.php');
    exit();
}

$error_message = '';
$success_message = '';

// Handle "back to login" request
if (isset($_GET['back_to_login'])) {
    unset($_SESSION['temp_admin_id']);
    unset($_SESSION['temp_admin_email']);
    header('Location: index.php');
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['login'])) {
        $username = sanitizeInput($_POST['username']);
        $password = trim($_POST['password']);

        if (empty($username) || empty($password)) {
            $error_message = 'Please fill in all fields.';
        } else {
            try {
                $pdo = getDBConnection();
                $stmt = $pdo->prepare("SELECT id, username, email, password FROM admins WHERE username = ? AND is_active = 1");
                $stmt->execute([$username]);
                $admin = $stmt->fetch();
                
                if ($admin && verifyPassword($password, $admin['password'])) {
                    // Generate OTP
                    $otp = sprintf('%06d', mt_rand(0, 999999));
                    $expires_at = date('Y-m-d H:i:s', time() + 600); // 10 minutes from now

                    // Store OTP in database
                    $stmt = $pdo->prepare("INSERT INTO otp_verifications (admin_id, otp_code, expires_at) VALUES (?, ?, ?)");
                    $stmt->execute([$admin['id'], $otp, $expires_at]);

                    // Store admin info in session temporarily
                    $_SESSION['temp_admin_id'] = $admin['id'];
                    $_SESSION['temp_admin_email'] = $admin['email'];

                    logSecurityEvent('login_success', ['username' => $username, 'admin_id' => $admin['id']]);

                    // Try to send OTP via email
                    $emailSent = false;
                    $emailError = '';

                    if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
                        $emailSent = sendOTPEmail($admin['email'], $admin['full_name'] ?? $admin['username'], $otp);
                        if (!$emailSent) {
                            $emailError = 'PHPMailer failed - check Gmail configuration';
                        }
                    } else {
                        $emailError = 'PHPMailer not installed';
                    }

                    if ($emailSent) {
                        $success_message = "OTP has been sent to your email address: " . substr($admin['email'], 0, 3) . "***@" . substr(strrchr($admin['email'], "@"), 1);
                    } else {
                        // Show OTP on screen with helpful message
                        $success_message = "Email setup needed ($emailError). For now, your OTP is: <strong>$otp</strong>";
                        $success_message .= "<br><small>To enable email: Install PHPMailer and configure Gmail in config/email.php</small>";
                    }

                    $show_otp_form = true;
                } else {
                    logSecurityEvent('login_failed', ['username' => $username]);
                    $error_message = 'Invalid username or password.';
                }
            } catch (PDOException $e) {
                $error_message = 'Database error occurred. Please try again.';
            }
        }
    } elseif (isset($_POST['verify_otp'])) {
        $otp = trim($_POST['otp']);
        
        if (empty($otp)) {
            $error_message = 'Please enter the OTP.';
        } elseif (!isset($_SESSION['temp_admin_id'])) {
            $error_message = 'Session expired. Please login again.';
        } else {
            try {
                $pdo = getDBConnection();
                $current_time = date('Y-m-d H:i:s');
                $stmt = $pdo->prepare("
                    SELECT ov.id, ov.admin_id, a.username
                    FROM otp_verifications ov
                    JOIN admins a ON ov.admin_id = a.id
                    WHERE ov.admin_id = ? AND ov.otp_code = ? AND ov.expires_at > ? AND ov.is_used = 0
                ");
                $stmt->execute([$_SESSION['temp_admin_id'], $otp, $current_time]);
                $otp_record = $stmt->fetch();
                
                if ($otp_record) {
                    // Mark OTP as used
                    $stmt = $pdo->prepare("UPDATE otp_verifications SET is_used = 1 WHERE id = ?");
                    $stmt->execute([$otp_record['id']]);
                    
                    // Set session variables
                    $_SESSION['admin_logged_in'] = true;
                    $_SESSION['admin_id'] = $otp_record['admin_id'];
                    $_SESSION['admin_username'] = $otp_record['username'];
                    
                    // Clear temporary session data
                    unset($_SESSION['temp_admin_id']);
                    unset($_SESSION['temp_admin_email']);
                    
                    header('Location: orders.php');
                    exit();
                } else {
                    $error_message = 'Invalid or expired OTP.';
                    $show_otp_form = true;
                }
            } catch (PDOException $e) {
                $error_message = 'Database error occurred. Please try again.';
                $show_otp_form = isset($_SESSION['temp_admin_id']);
            }
        }
    }
}

$show_otp_form = isset($_SESSION['temp_admin_id']);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - Borges Media</title>
    <link rel="stylesheet" href="../assets/css/admin-style.css">
</head>
<body>
    <div class="login-container">
        <div class="login-box">
            <div class="login-header">
                <h1>Administrator Login</h1>
                <p>Borges Media System</p>
            </div>
            
            <?php if ($error_message): ?>
                <div class="alert alert-error">
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <?php echo $success_message; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!$show_otp_form): ?>
                <!-- Login Form -->
                <form method="POST" class="login-form">
                    <div class="form-group">
                        <label for="username">Username:</label>
                        <input type="text" id="username" name="username" required 
                               value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="password">Password:</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    
                    <button type="submit" name="login" class="btn btn-primary">Login</button>
                </form>

                <div class="register-link">
                    <p>Don't have an account? <a href="register.php">Register here</a></p>
                </div>
            <?php else: ?>
                <!-- OTP Verification Form -->
                <form method="POST" class="login-form">
                    <div class="form-group">
                        <label for="otp">Enter OTP:</label>
                        <input type="text" id="otp" name="otp" maxlength="6" required 
                               placeholder="Enter 6-digit OTP">
                    </div>
                    
                    <button type="submit" name="verify_otp" class="btn btn-primary">Verify OTP</button>
                    <a href="index.php?back_to_login=1" class="btn btn-secondary">Back to Login</a>
                </form>

                <div class="register-link">
                    <p>Don't have an account? <a href="register.php">Register here</a></p>
                </div>
            <?php endif; ?>
            
            <div class="login-footer">
                <p>&copy; 2025 Borges Media System</p>
            </div>
        </div>
    </div>
    
    <script src="../assets/js/admin-script.js"></script>
</body>
</html>
