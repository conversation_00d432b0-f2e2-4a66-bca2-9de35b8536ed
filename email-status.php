<?php
// Email system status check
require_once 'config/security.php';

echo "<h1>📧 Email System Status</h1>";

// Check PHPMailer
echo "<h2>PHPMailer Status</h2>";
if (file_exists('vendor/autoload.php')) {
    require_once 'vendor/autoload.php';
    if (class_exists('P<PERSON><PERSON>ailer\PHPMailer\PHPMailer')) {
        echo "<p>✅ PHPMailer is installed and available</p>";
        $phpmailer_status = true;
    } else {
        echo "<p>❌ PHPMailer class not found</p>";
        $phpmailer_status = false;
    }
} else {
    echo "<p>❌ Composer autoload not found</p>";
    echo "<p>Run: <code>composer install</code> to install P<PERSON>Mailer</p>";
    $phpmailer_status = false;
}

// Check email configuration
echo "<h2>Gmail Configuration</h2>";
require_once 'config/email.php';

if (SMTP_USERNAME === '<EMAIL>') {
    echo "<p>❌ Gmail username not configured</p>";
    $config_status = false;
} else {
    echo "<p>✅ Gmail username: " . SMTP_USERNAME . "</p>";
    $config_status = true;
}

if (SMTP_PASSWORD === 'your-app-password') {
    echo "<p>❌ Gmail app password not configured</p>";
    $config_status = false;
} else {
    echo "<p>✅ Gmail app password: " . str_repeat('*', strlen(SMTP_PASSWORD)) . "</p>";
}

// Overall status
echo "<h2>Overall Status</h2>";
if ($phpmailer_status && $config_status) {
    echo "<p>🎉 <strong>Email system is ready!</strong></p>";
    echo "<p>OTP emails will be sent via Gmail SMTP</p>";
} elseif ($phpmailer_status && !$config_status) {
    echo "<p>⚠️ <strong>PHPMailer ready, configuration needed</strong></p>";
    echo "<p>Edit <code>config/email.php</code> with your Gmail credentials</p>";
} else {
    echo "<p>⚠️ <strong>Email system not ready</strong></p>";
    echo "<p>OTP will be displayed on screen instead</p>";
}

// Setup instructions
echo "<h2>Quick Setup</h2>";
echo "<ol>";
if (!$phpmailer_status) {
    echo "<li>Install PHPMailer: <code>composer install</code></li>";
}
if (!$config_status) {
    echo "<li>Edit <code>config/email.php</code></li>";
    echo "<li>Add your Gmail address and app password</li>";
}
echo "<li>Test login to verify email sending</li>";
echo "</ol>";

echo "<h2>Test Links</h2>";
echo "<p><a href='administrator/'>Test Admin Login</a></p>";
echo "<p><a href='GMAIL_SETUP_GUIDE.md'>View Setup Guide</a></p>";

// Show recent email logs
echo "<h2>Recent Email Logs</h2>";
$logFile = 'logs/security.log';
if (file_exists($logFile)) {
    $logs = file($logFile);
    $emailLogs = array_filter($logs, function($line) {
        return strpos($line, 'otp_email') !== false;
    });
    
    if (!empty($emailLogs)) {
        echo "<pre>";
        foreach (array_slice($emailLogs, -5) as $log) {
            echo htmlspecialchars($log);
        }
        echo "</pre>";
    } else {
        echo "<p>No email logs found yet</p>";
    }
} else {
    echo "<p>Log file not found</p>";
}
?>
