// ========================================
// INITIALIZATION METHODS
// ========================================

// Extend CF7TextEditor class with initialization methods
Object.assign(CF7TextEditor.prototype, {
    init() {
        this.setupCanvas();
        this.setupToolbar();
        this.setupEventListeners();
        this.initResponsiveScaling();

        // Initialize with disabled font controls - CF7 Pattern
        this.disableFontControls();
    },

    setupCanvas() {
        const width = this.canvas.dataset.width || 800;
        const height = this.canvas.dataset.height || 400;
        // Billboard aspect ratio is handled by CSS, but we set max dimensions
        this.canvas.style.maxWidth = width + 'px';
        this.canvas.style.maxHeight = height + 'px';
    },

    setupToolbar() {
        // Setup direct event listeners for HTML elements (no more shortcodes)
        this.setupDirectEventListeners();

        // Setup background controls
        this.setupBackgroundControls();

        // Setup font controls
        this.setupFontControls();
    },

    setupDirectEventListeners() {
        // Add Text Button
        const addTextBtn = this.container.querySelector('.cf7-btn-text');
        if (addTextBtn) {
            addTextBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.addTextElement();
            });
        }

        // Add Image Button
        const addImageBtn = this.container.querySelector('.cf7-btn-image');
        if (addImageBtn) {
            addImageBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.triggerImageUpload();
            });
        }

        // Clear Canvas Button
        const clearBtn = this.container.querySelector('.cf7-btn-clear');
        if (clearBtn) {
            clearBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.clearCanvas();
            });
        }

        // Font Style Buttons
        const boldBtn = this.container.querySelector('#cf7-font-bold');
        if (boldBtn) {
            boldBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleFontStyle('bold');
            });
        }

        const italicBtn = this.container.querySelector('#cf7-font-italic');
        if (italicBtn) {
            italicBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleFontStyle('italic');
            });
        }

        // Text Alignment Buttons
        ['left', 'center', 'right', 'justify'].forEach(align => {
            const alignBtn = this.container.querySelector(`#cf7-align-${align}`);
            if (alignBtn) {
                alignBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.setTextAlignment(align);
                });
            }
        });

        // Text Shadow Toggle
        const shadowBtn = this.container.querySelector('#cf7-text-shadow-toggle');
        if (shadowBtn) {
            shadowBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleTextShadow();
            });
        }

        // Font Controls
        this.setupFontInputListeners();
    },

    setupFontInputListeners() {
        // Font Family
        const fontFamily = this.container.querySelector('#cf7-font-family');
        if (fontFamily) {
            fontFamily.addEventListener('change', (e) => {
                this.setFontFamily(e.target.value);
            });
        }

        // Font Size
        const fontSize = this.container.querySelector('#cf7-font-size');
        if (fontSize) {
            fontSize.addEventListener('input', (e) => {
                this.setFontSize(e.target.value);
            });
        }

        // Font Color
        const fontColor = this.container.querySelector('#cf7-font-color');
        if (fontColor) {
            fontColor.addEventListener('input', (e) => {
                this.setFontColor(e.target.value);
            });
        }

        // Shadow Controls
        const shadowColor = this.container.querySelector('#cf7-shadow-color');
        if (shadowColor) {
            shadowColor.addEventListener('input', (e) => {
                this.setShadowColor(e.target.value);
            });
        }

        const shadowBlur = this.container.querySelector('#cf7-shadow-blur');
        if (shadowBlur) {
            shadowBlur.addEventListener('input', (e) => {
                this.setShadowBlur(e.target.value);
            });
        }

        const shadowOffsetX = this.container.querySelector('#cf7-shadow-offset-x');
        if (shadowOffsetX) {
            shadowOffsetX.addEventListener('input', (e) => {
                this.setShadowOffsetX(e.target.value);
            });
        }

        const shadowOffsetY = this.container.querySelector('#cf7-shadow-offset-y');
        if (shadowOffsetY) {
            shadowOffsetY.addEventListener('input', (e) => {
                this.setShadowOffsetY(e.target.value);
            });
        }

        const shadowOpacity = this.container.querySelector('#cf7-shadow-opacity-slider');
        if (shadowOpacity) {
            shadowOpacity.addEventListener('input', (e) => {
                this.setShadowOpacity(e.target.value);
                // Update the display value
                const valueDisplay = this.container.querySelector('#cf7-opacity-value');
                if (valueDisplay) {
                    valueDisplay.textContent = e.target.value + '%';
                }
            });
        }
    }
});