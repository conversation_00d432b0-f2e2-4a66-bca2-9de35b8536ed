# Borges Media - Simple PHP Web Project

A simple, non-MVC web project for billboard design services built with PHP, MySQL, CSS, and JavaScript.

## 📁 Project Structure

```
borges-media/
├── administrator/          # Admin panel
│   ├── includes/
│   │   └── auth.php       # Authentication functions
│   ├── index.php          # Admin login page
│   ├── orders.php         # Orders management
│   └── history.php        # Order history
├── customer/              # Customer interface
│   ├── includes/
│   ├── index.php          # Customer landing page
│   └── create-order.php   # Order creation handler
├── assets/                # Static assets
│   ├── css/
│   │   ├── admin-style.css
│   │   └── customer-style.css
│   ├── js/
│   │   ├── admin-script.js
│   │   └── customer-script.js
│   └── images/
├── config/                # Configuration files
│   ├── database.php       # Database connection
│   └── security.php       # Security functions
├── logs/                  # Log files (auto-created)
└── database_setup.sql     # Database setup script
```

## 🚀 Setup Instructions

### 1. Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx) or PHP built-in server

### 2. Database Setup
1. Create a MySQL database named `borges_media`
2. Import the database schema:
   ```sql
   mysql -u root -p borges_media < database_setup.sql
   ```
3. Update database credentials in `config/database.php` if needed

### 3. Configuration
1. Update database settings in `config/database.php`:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_USERNAME', 'your_username');
   define('DB_PASSWORD', 'your_password');
   define('DB_NAME', 'borges_media');
   ```

### 4. File Permissions
Ensure the following directories are writable:
- `logs/` (for security logs)
- `tmp/` (for session files, if using file-based sessions)

### 5. Running the Application

#### Using PHP Built-in Server (Development)
```bash
cd project-directory
php -S localhost:8000
```

#### Using Apache/Nginx
- Place files in your web server's document root
- Ensure mod_rewrite is enabled (for Apache)

## 🔐 Default Admin Credentials

- **Username:** admin
- **Password:** admin123
- **Email:** <EMAIL>

**Note:** The OTP will be displayed on screen for demo purposes. In production, implement email sending.

### Admin Registration

New administrators can register at `/administrator/register.php` with:
- Full name, username, email, and password
- Username uniqueness validation
- Email uniqueness validation
- Password confirmation
- Automatic password hashing

## 📱 Features

### Administrator Panel (`/administrator`)
- ✅ Admin registration system with validation
- ✅ Secure login with Gmail OTP verification
- ✅ Order management (view, update status)
- ✅ Order history tracking
- ✅ Session protection
- ✅ Responsive design

### Customer Interface (`/customer`)
- ✅ Landing page with service information
- ✅ Two billboard options: Templated & Custom
- ✅ Order submission form
- ✅ Mobile-responsive design
- ✅ Form validation

## 🛠️ Technical Features

### Security
- Password hashing with PHP's `password_hash()`
- Session security with HTTP-only cookies
- CSRF protection functions
- Input sanitization
- Security event logging

### Database
- PDO with prepared statements
- Transaction support
- Proper foreign key relationships
- Order history tracking

### Frontend
- Responsive CSS Grid and Flexbox
- Vanilla JavaScript (no frameworks)
- Mobile-first design
- Form validation
- Modal dialogs
- Smooth animations

## 📊 Database Tables

- `admins` - Administrator accounts
- `orders` - Billboard orders
- `billboard_templates` - Template definitions
- `order_history` - Status change tracking
- `otp_verifications` - OTP codes for login

## 🔧 Customization

### Adding New Order Statuses
1. Update the ENUM in the `orders` table
2. Add corresponding CSS classes in `admin-style.css`
3. Update the status dropdown in `orders.php`

### Styling Changes
- Admin styles: `assets/css/admin-style.css`
- Customer styles: `assets/css/customer-style.css`

### Adding New Features
- Follow the existing file structure
- Use the security functions in `config/security.php`
- Maintain the non-MVC approach with procedural PHP

## 🐛 Troubleshooting

### Database Connection Issues
1. Check credentials in `config/database.php`
2. Ensure MySQL service is running
3. Verify database exists and is accessible

### Session Issues
1. Check PHP session configuration
2. Ensure session directory is writable
3. Clear browser cookies

### Permission Issues
1. Set proper file permissions (755 for directories, 644 for files)
2. Ensure web server can write to logs directory

## 📝 Development Notes

- This is a flat PHP project (no MVC framework)
- Uses procedural PHP with some functional organization
- Follows security best practices for a simple application
- Mobile-responsive using CSS media queries
- Vanilla JavaScript for all interactions

## 🚀 Production Deployment

1. Enable HTTPS and update session security settings
2. Set up proper email sending for OTP
3. Configure proper logging and monitoring
4. Set up database backups
5. Update default admin credentials
6. Configure proper error handling

## 📄 License

This project is for educational/demonstration purposes.
