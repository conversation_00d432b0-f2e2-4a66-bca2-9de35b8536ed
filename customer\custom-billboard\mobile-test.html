<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <title>Mobile Text Input Test - Billboard Designer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-title {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: bold;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.4;
        }
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px 10px 0;
        }
        .test-button:hover {
            background: #005a87;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>Mobile Text Input Test</h1>
    <p>This page helps test the mobile text input fixes for the Billboard Designer.</p>

    <div class="test-container">
        <div class="test-title">Test 1: Device Detection</div>
        <div class="test-description">Check if we can properly detect mobile devices and iOS Safari.</div>
        <button class="test-button" onclick="testDeviceDetection()">Run Device Detection Test</button>
        <div id="device-status" class="status" style="display: none;"></div>
    </div>

    <div class="test-container">
        <div class="test-title">Test 2: Touch Event Support</div>
        <div class="test-description">Verify that touch events are properly supported.</div>
        <button class="test-button" onclick="testTouchEvents()">Test Touch Events</button>
        <div id="touch-status" class="status" style="display: none;"></div>
    </div>

    <div class="test-container">
        <div class="test-title">Test 3: ContentEditable Focus</div>
        <div class="test-description">Test if contenteditable elements can be focused on mobile.</div>
        <button class="test-button" onclick="testContentEditableFocus()">Test Focus</button>
        <div id="focus-status" class="status" style="display: none;"></div>
        <div id="test-editable" contenteditable="true" style="border: 1px solid #ccc; padding: 10px; margin-top: 10px; min-height: 40px; display: none;">
            Tap here to test text input...
        </div>
    </div>

    <div class="test-container">
        <div class="test-title">Test 4: Billboard Designer Integration</div>
        <div class="test-description">
            Open the main billboard designer to test the actual functionality.<br><br>
            <strong>Testing Instructions:</strong><br>
            1. Click "Add Text" to create a text element<br>
            2. Drag the text element to move it around<br>
            3. <strong>Single tap or double tap</strong> the text element to enter edit mode<br>
            4. Try typing - the mobile keyboard should appear<br>
            5. Tap outside to exit edit mode
        </div>
        <button class="test-button" onclick="openBillboardDesigner()">Open Billboard Designer</button>
        <div id="integration-status" class="status" style="display: none;"></div>
    </div>

    <script>
        function testDeviceDetection() {
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
            const isAndroid = /Android/i.test(navigator.userAgent);
            
            const statusDiv = document.getElementById('device-status');
            statusDiv.style.display = 'block';
            
            let message = `User Agent: ${navigator.userAgent}<br><br>`;
            message += `Mobile Device: ${isMobile ? 'Yes' : 'No'}<br>`;
            message += `iOS Device: ${isIOS ? 'Yes' : 'No'}<br>`;
            message += `Android Device: ${isAndroid ? 'Yes' : 'No'}<br>`;
            message += `Touch Support: ${'ontouchstart' in window ? 'Yes' : 'No'}<br>`;
            message += `Screen Size: ${window.screen.width}x${window.screen.height}<br>`;
            message += `Viewport Size: ${window.innerWidth}x${window.innerHeight}`;
            
            statusDiv.innerHTML = message;
            statusDiv.className = 'status info';
        }

        function testTouchEvents() {
            const statusDiv = document.getElementById('touch-status');
            statusDiv.style.display = 'block';
            
            if ('ontouchstart' in window) {
                statusDiv.innerHTML = 'Touch events are supported! Try touching this message.';
                statusDiv.className = 'status success';
                
                statusDiv.addEventListener('touchstart', function(e) {
                    statusDiv.innerHTML = 'Touch detected! Touch events are working properly.';
                }, { passive: true });
            } else {
                statusDiv.innerHTML = 'Touch events are not supported on this device.';
                statusDiv.className = 'status error';
            }
        }

        function testContentEditableFocus() {
            const statusDiv = document.getElementById('focus-status');
            const editableDiv = document.getElementById('test-editable');
            
            statusDiv.style.display = 'block';
            editableDiv.style.display = 'block';
            
            // Apply the same styles as our billboard designer
            editableDiv.style.touchAction = 'manipulation';
            editableDiv.style.webkitUserSelect = 'text';
            editableDiv.style.userSelect = 'text';
            editableDiv.style.webkitTouchCallout = 'default';
            editableDiv.style.fontSize = 'max(16px, 1em)';
            
            try {
                editableDiv.focus();
                statusDiv.innerHTML = 'ContentEditable element is ready for testing. Try tapping the text area below.';
                statusDiv.className = 'status success';
                
                editableDiv.addEventListener('focus', function() {
                    statusDiv.innerHTML = 'SUCCESS: ContentEditable focus is working!';
                    statusDiv.className = 'status success';
                });
                
                editableDiv.addEventListener('blur', function() {
                    statusDiv.innerHTML = 'ContentEditable lost focus. Try tapping it again.';
                    statusDiv.className = 'status info';
                });
                
            } catch (error) {
                statusDiv.innerHTML = `Error testing focus: ${error.message}`;
                statusDiv.className = 'status error';
            }
        }

        function openBillboardDesigner() {
            const statusDiv = document.getElementById('integration-status');
            statusDiv.style.display = 'block';
            statusDiv.innerHTML = 'Opening Billboard Designer in a new tab...';
            statusDiv.className = 'status info';
            
            window.open('index.php', '_blank');
        }

        // Auto-run device detection on page load
        window.addEventListener('load', function() {
            testDeviceDetection();
        });
    </script>
</body>
</html>
