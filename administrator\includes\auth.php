<?php
// Authentication and session management functions

require_once __DIR__ . '/../../config/security.php';
require_once __DIR__ . '/../../config/database.php';

// Check if admin is logged in
function isAdminLoggedIn() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

// Require admin login - redirect if not logged in
function requireAdminLogin() {
    if (!isAdminLoggedIn()) {
        header('Location: index.php');
        exit();
    }
}

// Get current admin info
function getCurrentAdmin() {
    if (isAdminLoggedIn()) {
        return [
            'id' => $_SESSION['admin_id'],
            'username' => $_SESSION['admin_username']
        ];
    }
    return null;
}

// Logout function
function adminLogout() {
    logSecurityEvent('admin_logout', ['admin_id' => $_SESSION['admin_id'] ?? null]);
    session_unset();
    session_destroy();
    header('Location: index.php');
    exit();
}

// Handle logout request
if (isset($_GET['logout'])) {
    adminLogout();
}
?>
