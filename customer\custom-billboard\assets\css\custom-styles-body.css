
        /* CF7 Compatible Fixed Header - Following WordPress Ad<PERSON> */
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f0f0f0;
            padding-top: 80px; /* Space for fixed header */
        }

        .cf7-fixed-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #23282d;
            color: white;
            padding: 15px 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            z-index: 9999;
            border-bottom: 1px solid #32373c;
        }

        .cf7-header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .cf7-header-brand {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .cf7-header-logo {
            width: 32px;
            height: 32px;
            background: #0073aa;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
            color: white;
        }

        .cf7-header-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
            color: #f1f1f1;
        }

        .cf7-header-subtitle {
            font-size: 12px;
            color: #a0a5aa;
            margin: 0;
        }

        .cf7-header-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .cf7-nav-link {
            color: #a0a5aa;
            text-decoration: none;
            font-size: 14px;
            padding: 8px 12px;
            border-radius: 3px;
            transition: all 0.2s ease;
        }

        .cf7-nav-link:hover,
        .cf7-nav-link.active {
            color: #00a0d2;
            background: rgba(0, 160, 210, 0.1);
        }

        /* Main Layout Container */
        .cf7-main-layout {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 280px 1fr;
            gap: 20px;
            padding: 20px;
            min-height: calc(100vh - 80px);
        }

        /* CF7 Pinned Posts Sidebar - Following WordPress Admin Patterns */
        .cf7-pinned-sidebar {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid #ddd;
            height: fit-content;
            position: sticky;
            top: 100px; /* Below fixed header */
        }

        .cf7-sidebar-header {
            padding: 16px 20px;
            border-bottom: 1px solid #e1e1e1;
            background: #f8f9fa;
            border-radius: 8px 8px 0 0;
        }

        .cf7-sidebar-title {
            font-size: 16px;
            font-weight: 600;
            color: #23282d;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .cf7-pin-icon {
            width: 16px;
            height: 16px;
            background: #0073aa;
            border-radius: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
            font-weight: bold;
        }

        .cf7-pinned-posts {
            padding: 0;
            margin: 0;
            list-style: none;
        }

        .cf7-pinned-post {
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s ease;
        }

        .cf7-pinned-post:last-child {
            border-bottom: none;
        }

        .cf7-pinned-post:hover {
            background: #f8f9fa;
        }

        .cf7-post-link {
            display: block;
            padding: 16px 20px;
            text-decoration: none;
            color: inherit;
        }

        .cf7-post-title {
            font-size: 14px;
            font-weight: 500;
            color: #23282d;
            margin: 0 0 6px 0;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .cf7-post-meta {
            font-size: 12px;
            color: #666;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .cf7-post-date {
            color: #0073aa;
        }

        .cf7-post-category {
            background: #e7f3ff;
            color: #0073aa;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 500;
        }

        /* Main Content Area */
        .cf7-main-content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid #ddd;
            overflow: hidden;
        }

        /* Responsive Design - CF7 Compatible */
        @media (max-width: 768px) {
            .cf7-main-layout {
                grid-template-columns: 1fr;
                padding: 10px;
            }

            .cf7-pinned-sidebar {
                order: 2;
                position: static;
                margin-top: 20px;
            }

            .cf7-main-content {
                order: 1;
            }

            .cf7-header-content {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .cf7-header-nav {
                gap: 10px;
                flex-wrap: wrap;
                justify-content: center;
            }

            body {
                padding-top: 120px; /* More space for mobile header */
            }
        }

        @media (max-width: 480px) {
            .cf7-header-nav {
                display: none; /* Hide nav on very small screens */
            }

            body {
                padding-top: 80px;
            }
        }
        .usage-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .usage-info h3 {
            margin: 0 0 10px 0;
            color: #0073aa;
        }
        .code {
            background: #f1f1f1;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            border-left: 4px solid #0073aa;
        }