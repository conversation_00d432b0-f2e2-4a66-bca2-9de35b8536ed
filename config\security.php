<?php
// Security configuration and functions

// Start secure session
function startSecureSession() {
    // Session configuration for security
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 0); // Set to 1 for HTTPS
    ini_set('session.use_strict_mode', 1);
    ini_set('session.cookie_samesite', 'Strict');
    
    // Set session name
    session_name('BORGES_MEDIA_ADMIN_SESSION');
    
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Regenerate session ID periodically for security
    if (!isset($_SESSION['last_regeneration'])) {
        $_SESSION['last_regeneration'] = time();
    } elseif (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }
}

// CSRF Protection
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Input sanitization
function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// Validate email
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Validate phone number
function isValidPhone($phone) {
    $phone = preg_replace('/[^0-9+]/', '', $phone);
    return preg_match('/^\+?[1-9]\d{9,14}$/', $phone);
}

// Password hashing
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// Password verification
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// Rate limiting for login attempts (DISABLED)
/*
function checkRateLimit($identifier, $maxAttempts = 5, $timeWindow = 900) { // 15 minutes
    $cacheFile = sys_get_temp_dir() . '/login_attempts_' . md5($identifier);

    $attempts = [];
    if (file_exists($cacheFile)) {
        $attempts = json_decode(file_get_contents($cacheFile), true) ?: [];
    }

    // Clean old attempts
    $currentTime = time();
    $attempts = array_filter($attempts, function($timestamp) use ($currentTime, $timeWindow) {
        return ($currentTime - $timestamp) < $timeWindow;
    });

    // Check if limit exceeded
    if (count($attempts) >= $maxAttempts) {
        return false;
    }

    // Record this attempt
    $attempts[] = $currentTime;
    file_put_contents($cacheFile, json_encode($attempts));

    return true;
}

// Clear rate limit (on successful login)
function clearRateLimit($identifier) {
    $cacheFile = sys_get_temp_dir() . '/login_attempts_' . md5($identifier);
    if (file_exists($cacheFile)) {
        unlink($cacheFile);
    }
}
*/

// Generate secure random string
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

// Log security events
function logSecurityEvent($event, $details = []) {
    $logFile = __DIR__ . '/../logs/security.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'event' => $event,
        'details' => $details
    ];
    
    file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
}

// Check if request is from allowed IP (optional)
function checkAllowedIP($allowedIPs = []) {
    if (empty($allowedIPs)) {
        return true; // No IP restriction
    }
    
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? '';
    return in_array($clientIP, $allowedIPs);
}

// Validate session integrity
function validateSessionIntegrity() {
    // Check if session variables are consistent
    if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
        if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_username'])) {
            return false;
        }
        
        // Additional checks can be added here
        // e.g., verify admin still exists in database
    }
    
    return true;
}

// Clean expired sessions (call periodically)
function cleanExpiredSessions() {
    $sessionPath = session_save_path();
    if (empty($sessionPath)) {
        $sessionPath = sys_get_temp_dir();
    }
    
    $files = glob($sessionPath . '/sess_*');
    $now = time();
    $maxLifetime = ini_get('session.gc_maxlifetime');
    
    foreach ($files as $file) {
        if (filemtime($file) + $maxLifetime < $now) {
            unlink($file);
        }
    }
}

// Initialize security
startSecureSession();

// Check session integrity on each request
if (!validateSessionIntegrity()) {
    session_destroy();
    if (strpos($_SERVER['REQUEST_URI'], '/administrator/') !== false) {
        header('Location: /administrator/index.php');
        exit();
    }
}
?>
