<?php
require_once 'includes/auth.php';
requireAdminLogin();

require_once '../config/database.php';

$current_admin = getCurrentAdmin();

// Handle status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $order_id = (int)$_POST['order_id'];
    $new_status = $_POST['status'];
    $notes = trim($_POST['notes']);
    
    try {
        $pdo = getDBConnection();
        
        // Get current status
        $stmt = $pdo->prepare("SELECT status FROM orders WHERE id = ?");
        $stmt->execute([$order_id]);
        $current_status = $stmt->fetchColumn();
        
        // Update order status
        $stmt = $pdo->prepare("UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$new_status, $order_id]);
        
        // Add to history
        $stmt = $pdo->prepare("INSERT INTO order_history (order_id, status_from, status_to, notes, changed_by) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$order_id, $current_status, $new_status, $notes, $current_admin['id']]);
        
        $success_message = 'Order status updated successfully.';
    } catch (PDOException $e) {
        $error_message = 'Error updating order status.';
    }
}

// Get filter parameters
$status_filter = $_GET['status'] ?? '';
$type_filter = $_GET['type'] ?? '';

// Build query
$where_conditions = [];
$params = [];

if ($status_filter) {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

if ($type_filter) {
    $where_conditions[] = "billboard_type = ?";
    $params[] = $type_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

try {
    $pdo = getDBConnection();
    
    // Get orders
    $query = "SELECT * FROM orders $where_clause ORDER BY created_at DESC";
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $orders = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error_message = 'Database error occurred.';
    $orders = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Orders - Borges Media Admin</title>
    <link rel="stylesheet" href="../assets/css/admin-style.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-content">
                <h1>Borges Media Admin</h1>
                <div class="header-user">
                    Welcome, <?php echo htmlspecialchars($current_admin['username']); ?>
                </div>
            </div>
        </header>
        
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="orders.php" class="active">List of Orders</a></li>
                    <li><a href="history.php">History</a></li>
                    <li><a href="?logout=1" class="logout-btn">Logout</a></li>
                </ul>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="admin-main">
            <div class="main-content">
                <h2>List of Orders</h2>
                
                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success"><?php echo $success_message; ?></div>
                <?php endif; ?>
                
                <?php if (isset($error_message)): ?>
                    <div class="alert alert-error"><?php echo $error_message; ?></div>
                <?php endif; ?>
                
                <!-- Filters -->
                <div class="filters">
                    <form method="GET" class="filter-form">
                        <div class="filter-group">
                            <label for="status">Status:</label>
                            <select name="status" id="status">
                                <option value="">All Statuses</option>
                                <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                <option value="in_progress" <?php echo $status_filter === 'in_progress' ? 'selected' : ''; ?>>In Progress</option>
                                <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                                <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="type">Type:</label>
                            <select name="type" id="type">
                                <option value="">All Types</option>
                                <option value="templated" <?php echo $type_filter === 'templated' ? 'selected' : ''; ?>>Templated</option>
                                <option value="custom" <?php echo $type_filter === 'custom' ? 'selected' : ''; ?>>Custom</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Filter</button>
                        <a href="orders.php" class="btn btn-secondary">Clear</a>
                    </form>
                </div>
                
                <!-- Orders Table -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Order #</th>
                                <th>Customer</th>
                                <th>Email</th>
                                <th>Type</th>
                                <th>Title</th>
                                <th>Status</th>
                                <th>Deadline</th>
                                <th>Amount</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($orders)): ?>
                                <?php foreach ($orders as $order): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($order['order_number']); ?></td>
                                        <td><?php echo htmlspecialchars($order['customer_name']); ?></td>
                                        <td><?php echo htmlspecialchars($order['customer_email']); ?></td>
                                        <td>
                                            <span class="badge badge-<?php echo $order['billboard_type']; ?>">
                                                <?php echo ucfirst($order['billboard_type']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($order['title']); ?></td>
                                        <td>
                                            <span class="status status-<?php echo $order['status']; ?>">
                                                <?php echo ucfirst(str_replace('_', ' ', $order['status'])); ?>
                                            </span>
                                        </td>
                                        <td><?php echo $order['deadline'] ? date('M j, Y', strtotime($order['deadline'])) : 'N/A'; ?></td>
                                        <td>$<?php echo number_format($order['total_amount'], 2); ?></td>
                                        <td>
                                            <button onclick="openStatusModal(<?php echo $order['id']; ?>, '<?php echo $order['status']; ?>')" 
                                                    class="btn btn-sm">Update</button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="9" class="text-center">No orders found</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Status Update Modal -->
    <div id="statusModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>Update Order Status</h3>
            <form method="POST">
                <input type="hidden" id="modal_order_id" name="order_id">
                
                <div class="form-group">
                    <label for="modal_status">New Status:</label>
                    <select id="modal_status" name="status" required>
                        <option value="pending">Pending</option>
                        <option value="in_progress">In Progress</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="modal_notes">Notes (optional):</label>
                    <textarea id="modal_notes" name="notes" rows="3"></textarea>
                </div>
                
                <button type="submit" name="update_status" class="btn btn-primary">Update Status</button>
            </form>
        </div>
    </div>
    
    <script src="../assets/js/admin-script.js"></script>
</body>
</html>
