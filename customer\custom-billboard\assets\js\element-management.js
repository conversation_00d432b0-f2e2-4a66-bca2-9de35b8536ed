// ========================================
// ELEMENT MANAGEMENT METHODS
// ========================================

// Extend CF7TextEditor class with element management methods
Object.assign(CF7TextEditor.prototype, {
    addTextElement() {
            this.elementCounter++;

            // Create container element (non-editable)
            const textElement = document.createElement('div');
            textElement.className = 'cf7-draggable-text';
            textElement.style.left = '50px';
            textElement.style.top = '50px';
            textElement.style.width = '150px';
            textElement.style.height = '40px';
            textElement.setAttribute('data-element-id', `text-${this.elementCounter}`);

            // Create separate editable content area
            const editableContent = document.createElement('div');
            editableContent.className = 'cf7-editable-content';
            editableContent.contentEditable = true;
            editableContent.textContent = `Text ${this.elementCounter}`;
            editableContent.setAttribute('data-placeholder', 'Enter text...');

            // Add delete button with enhanced functionality
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'cf7-delete-btn';
            deleteBtn.innerHTML = '×';
            deleteBtn.title = 'Delete text element';
            deleteBtn.setAttribute('aria-label', 'Delete text element');
            deleteBtn.setAttribute('contenteditable', 'false'); // Prevent editing

            // Enhanced event handling for delete button
            deleteBtn.onmousedown = (e) => {
                e.stopPropagation(); // Prevent drag start
                e.preventDefault();
            };

            deleteBtn.onclick = (e) => {
                e.stopPropagation();
                e.preventDefault();
                this.deleteElement(textElement);
            };

            // Add resize handles
            this.addResizeHandles(textElement);

            // Append elements in correct order
            textElement.appendChild(editableContent);
            textElement.appendChild(deleteBtn);

            // Setup enhanced element events
            this.setupElementEvents(textElement);
            this.setupTextEditingEvents(textElement, editableContent);

            this.elementsContainer.appendChild(textElement);
            this.selectElement(textElement);

            // Handle responsive scaling for new element
            if (this.onElementAdded) {
                this.onElementAdded(textElement);
            }
        },

        addResizeHandles(element) {
            const handles = ['nw', 'ne', 'sw', 'se'];
            handles.forEach(direction => {
                const handle = document.createElement('div');
                handle.className = `cf7-resize-handle cf7-resize-${direction}`;
                handle.setAttribute('contenteditable', 'false'); // Prevent editing
                handle.setAttribute('data-no-edit', 'true'); // Additional protection

                // Enhanced event handling - Mouse events
                handle.addEventListener('mousedown', (e) => {
                    e.stopPropagation(); // Prevent text selection
                    this.handleResizeDown(e, element, direction);
                });

                // Touch events for mobile support
                handle.addEventListener('touchstart', (e) => {
                    e.stopPropagation(); // Prevent text selection
                    this.handleResizeTouchStart(e, element, direction);
                }, { passive: false });

                // Prevent text editing interference
                handle.addEventListener('click', (e) => {
                    e.stopPropagation();
                    e.preventDefault();
                });

                element.appendChild(handle);
            });
        },

        setupTextEditingEvents(textElement, editableContent) {
            // Store reference to editable content
            textElement._editableContent = editableContent;

            // Enhanced text editing event handling
            editableContent.addEventListener('focus', (e) => {
                e.stopPropagation();
                textElement.classList.add('cf7-editing');
                this.selectElement(textElement);
            });

            editableContent.addEventListener('blur', (e) => {
                e.stopPropagation();
                textElement.classList.remove('cf7-editing');
                this.preserveElementStructure(textElement);
            });

            // CF7 Pattern: Conditional event handling for drag vs edit
            editableContent.addEventListener('mousedown', (e) => {
                // Only stop propagation if actively editing (focused)
                if (document.activeElement === editableContent) {
                    e.stopPropagation();
                } else {
                    // Allow drag to work by not stopping propagation
                    // This follows CF7 pattern of conditional event handling
                }
            });

            // Enhanced touch event handling for mobile text input
            editableContent.addEventListener('touchstart', (e) => {
                // Always stop propagation for touch on editable content
                e.stopPropagation();

                // If already focused (editing), allow normal text input behavior
                if (document.activeElement === editableContent || textElement.classList.contains('cf7-editing')) {
                    // Don't prevent default - allow text input
                    return;
                }

                // Mobile focus fix: Handle tap to focus for iOS Safari
                // Don't prevent default to allow focus, but ensure proper setup
                this.handleMobileFocus(editableContent, textElement, e);
            }, { passive: false });

            // Handle text changes with structure preservation
            editableContent.addEventListener('input', (e) => {
                this.handleTextInput(textElement, editableContent);
            });

            // Handle keyboard events
            editableContent.addEventListener('keydown', (e) => {
                this.handleTextKeydown(e, textElement, editableContent);
            });

            // CF7 Pattern: Double click to enter edit mode (desktop)
            textElement.addEventListener('dblclick', (e) => {
                if (!e.target.classList.contains('cf7-editable-content')) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.enterEditMode(editableContent, textElement);
                }
            });

            // Mobile double-tap detection for text editing
            let lastTouchTime = 0;
            let touchCount = 0;

            textElement.addEventListener('touchend', (e) => {
                const currentTime = new Date().getTime();
                const tapLength = currentTime - lastTouchTime;

                // If this is a quick tap (less than 500ms since last tap)
                if (tapLength < 500 && tapLength > 0) {
                    touchCount++;

                    // Double tap detected
                    if (touchCount === 2) {
                        e.preventDefault();
                        e.stopPropagation();

                        // Reset touch count
                        touchCount = 0;

                        // Enter edit mode
                        this.enterEditMode(editableContent, textElement);
                        return;
                    }
                } else {
                    // Reset if too much time has passed
                    touchCount = 1;
                }

                lastTouchTime = currentTime;

                // Single tap behavior - also try to enter edit mode after a delay
                // This provides a fallback if double-tap doesn't work
                setTimeout(() => {
                    if (touchCount === 1 && !textElement.classList.contains('cf7-editing')) {
                        // Single tap detected, try to enter edit mode
                        this.enterEditMode(editableContent, textElement);
                        touchCount = 0;
                    }
                }, 300);
            }, { passive: false });

            // CF7 Pattern: Single click behavior for editable content (desktop)
            editableContent.addEventListener('click', (e) => {
                // If not in editing mode, single click should focus for editing
                if (!textElement.classList.contains('cf7-editing')) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.enterEditMode(editableContent, textElement);
                } else {
                    // In editing mode, allow normal text cursor positioning
                    e.stopPropagation();
                }
            });
        },

        triggerImageUpload() {
            // Create a hidden file input following CF7 patterns
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = 'image/*';
            fileInput.className = 'cf7-file-input';
            fileInput.style.display = 'none';

            // Add event listener for file selection
            fileInput.addEventListener('change', this.addImageElement());

            // Trigger file selection
            document.body.appendChild(fileInput);
            fileInput.click();
            document.body.removeChild(fileInput);
        },

        addImageElement() {
            // This will be called when file is selected
            return (event) => {
                const file = event.target.files[0];
                if (!file || !file.type.startsWith('image/')) return;

                const reader = new FileReader();
                reader.onload = (e) => {
                    this.elementCounter++;
                    const imageElement = document.createElement('div');
                    imageElement.className = 'cf7-draggable-image';
                    imageElement.style.left = '50px';
                    imageElement.style.top = '50px';
                    imageElement.style.width = '150px';
                    imageElement.style.height = '150px';

                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.alt = `Image ${this.elementCounter}`;
                    imageElement.appendChild(img);

                    // Add delete button with enhanced functionality
                    const deleteBtn = document.createElement('button');
                    deleteBtn.className = 'cf7-delete-btn';
                    deleteBtn.innerHTML = '×';
                    deleteBtn.title = 'Delete image element';
                    deleteBtn.setAttribute('aria-label', 'Delete image element');

                    // Enhanced event handling
                    deleteBtn.onmousedown = (e) => {
                        e.stopPropagation(); // Prevent drag start
                        e.preventDefault();
                    };

                    deleteBtn.onclick = (e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        this.deleteElement(imageElement);
                    };

                    imageElement.appendChild(deleteBtn);

                    // Add resize handles for images (following CF7 patterns)
                    this.addResizeHandles(imageElement);

                    this.setupElementEvents(imageElement);
                    this.elementsContainer.appendChild(imageElement);
                    this.selectElement(imageElement);

                    // Handle responsive scaling for new element
                    if (this.onElementAdded) {
                        this.onElementAdded(imageElement);
                    }
                };
                reader.readAsDataURL(file);

                // Reset file input
                event.target.value = '';
            };
        },

        setupElementEvents(element) {
            // CF7 Pattern: Main element mouse events
            element.addEventListener('mousedown', (e) => this.handleMouseDown(e, element));

            // CF7 Pattern: Main element touch events for mobile support
            element.addEventListener('touchstart', (e) => this.handleTouchStart(e, element), { passive: false });

            element.addEventListener('click', (e) => {
                // CF7 Pattern: Conditional event handling
                // Only stop propagation if not clicking on editable content or controls
                if (!e.target.classList.contains('cf7-editable-content') &&
                    !e.target.classList.contains('cf7-delete-btn') &&
                    !e.target.classList.contains('cf7-resize-handle')) {
                    e.stopPropagation();
                    this.selectElement(element);
                }
            });

            // Handle image resizing
            if (element.classList.contains('cf7-draggable-image')) {
                // Double click to reset size
                element.addEventListener('dblclick', () => {
                    element.style.width = '150px';
                    element.style.height = '150px';
                });
            }
        },

        handleTextInput(textElement, editableContent) {
            // Ensure structure is preserved after text input
            this.preserveElementStructure(textElement);

            // Handle empty text placeholder
            if (editableContent.textContent.trim() === '') {
                editableContent.classList.add('cf7-empty');
            } else {
                editableContent.classList.remove('cf7-empty');
            }
        },

        handleTextKeydown(e, textElement, editableContent) {
            // Handle special keys
            if (e.key === 'Escape') {
                e.preventDefault();
                editableContent.blur();
                return;
            }

            // Prevent certain keys that might affect structure
            if (e.key === 'Delete' && editableContent.textContent.trim() === '') {
                e.preventDefault();
                return;
            }

            // Allow normal text editing
            e.stopPropagation();
        },

        preserveElementStructure(textElement) {
            // Ensure all required child elements are present
            const editableContent = textElement.querySelector('.cf7-editable-content');
            const deleteBtn = textElement.querySelector('.cf7-delete-btn');
            const resizeHandles = textElement.querySelectorAll('.cf7-resize-handle');

            // Re-add delete button if missing
            if (!deleteBtn) {
                const newDeleteBtn = document.createElement('button');
                newDeleteBtn.className = 'cf7-delete-btn';
                newDeleteBtn.innerHTML = '×';
                newDeleteBtn.title = 'Delete text element';
                newDeleteBtn.setAttribute('aria-label', 'Delete text element');
                newDeleteBtn.setAttribute('contenteditable', 'false');

                newDeleteBtn.onmousedown = (e) => {
                    e.stopPropagation();
                    e.preventDefault();
                };

                newDeleteBtn.onclick = (e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    this.deleteElement(textElement);
                };

                textElement.appendChild(newDeleteBtn);
            }

            // Re-add resize handles if missing
            if (resizeHandles.length < 4) {
                // Remove any existing handles first
                resizeHandles.forEach(handle => handle.remove());
                // Re-add all handles
                this.addResizeHandles(textElement);
            }

            // Ensure editable content is properly configured
            if (editableContent) {
                editableContent.setAttribute('contenteditable', 'true');
                if (!editableContent.hasAttribute('data-placeholder')) {
                    editableContent.setAttribute('data-placeholder', 'Enter text...');
                }
            }
        },

        // Mobile-specific focus management for iOS Safari and other mobile browsers
        handleMobileFocus(editableContent, textElement, touchEvent) {
            // Detect if we're on a mobile device
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);

            if (!isMobile) {
                // Not mobile, use standard focus
                editableContent.focus();
                textElement.classList.add('cf7-editing');
                this.selectElement(textElement);
                return;
            }

            // Mobile focus handling
            // For iOS Safari, we need special handling due to focus restrictions
            if (isIOS) {
                // iOS requires focus to be triggered directly from user interaction
                // Don't prevent the default touch behavior

                // Set up the element for editing
                textElement.classList.add('cf7-editing');
                this.selectElement(textElement);

                // Ensure contenteditable is properly set
                editableContent.setAttribute('contenteditable', 'true');
                editableContent.style.touchAction = 'manipulation';

                // Try to focus immediately (works if called from user interaction)
                editableContent.focus();

                // Fallback: If focus didn't work, set up for next touch
                setTimeout(() => {
                    if (document.activeElement !== editableContent) {
                        // Focus didn't work, prepare for next touch
                        editableContent.setAttribute('tabindex', '0');

                        // Add a one-time touch listener for the next touch
                        const focusOnNextTouch = (e) => {
                            e.stopPropagation();
                            editableContent.focus();
                            editableContent.removeEventListener('touchstart', focusOnNextTouch);
                            editableContent.removeAttribute('tabindex');
                        };

                        editableContent.addEventListener('touchstart', focusOnNextTouch, { once: true, passive: false });
                    }
                }, 100);

            } else {
                // Android and other mobile browsers
                textElement.classList.add('cf7-editing');
                this.selectElement(textElement);

                // Ensure proper setup for text input
                editableContent.setAttribute('contenteditable', 'true');
                editableContent.style.touchAction = 'manipulation';

                // Focus with a small delay to ensure touch event completes
                setTimeout(() => {
                    editableContent.focus();
                }, 50);
            }
        },

        // Unified method to enter edit mode for text elements
        enterEditMode(editableContent, textElement) {
            // Prevent entering edit mode if already editing
            if (textElement.classList.contains('cf7-editing')) {
                return;
            }

            // Add editing class
            textElement.classList.add('cf7-editing');
            this.selectElement(textElement);

            // Ensure contenteditable is set
            editableContent.setAttribute('contenteditable', 'true');

            // Mobile-specific setup
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

            if (isMobile) {
                // Mobile focus handling
                editableContent.style.touchAction = 'manipulation';
                editableContent.style.webkitUserSelect = 'text';
                editableContent.style.userSelect = 'text';

                // For iOS, ensure proper focus
                if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
                    // iOS requires special handling
                    editableContent.setAttribute('tabindex', '0');

                    // Try immediate focus
                    editableContent.focus();

                    // Fallback with delay
                    setTimeout(() => {
                        if (document.activeElement !== editableContent) {
                            editableContent.focus();
                        }

                        // Set cursor at end of text
                        try {
                            const range = document.createRange();
                            const selection = window.getSelection();
                            range.selectNodeContents(editableContent);
                            range.collapse(false);
                            selection.removeAllRanges();
                            selection.addRange(range);
                        } catch (e) {
                            // Ignore range errors on mobile
                            console.log('Range selection not supported on this device');
                        }
                    }, 100);
                } else {
                    // Android and other mobile browsers
                    setTimeout(() => {
                        editableContent.focus();

                        // Set cursor at end of text
                        try {
                            const range = document.createRange();
                            const selection = window.getSelection();
                            range.selectNodeContents(editableContent);
                            range.collapse(false);
                            selection.removeAllRanges();
                            selection.addRange(range);
                        } catch (e) {
                            // Ignore range errors on mobile
                            console.log('Range selection not supported on this device');
                        }
                    }, 50);
                }
            } else {
                // Desktop focus handling
                editableContent.focus();

                // Set cursor at end of text
                const range = document.createRange();
                const selection = window.getSelection();
                range.selectNodeContents(editableContent);
                range.collapse(false);
                selection.removeAllRanges();
                selection.addRange(range);
            }
        }
});