/* Customer Landing Page Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #28a745 0%, #ff8c00 100%);
    color: white;
    padding: 20px 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.logo h1 {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 5px;
}

.logo p {
    font-size: 14px;
    opacity: 0.9;
}

.nav {
    display: flex;
    gap: 30px;
}

.nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: opacity 0.3s;
}

.nav a:hover {
    opacity: 0.8;
}

/* Hero Section */
.hero {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8fff9 0%, #fff3e0 100%);
}

.hero-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero-content h2 {
    font-size: 48px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #2c3e50;
}

.hero-content > p {
    font-size: 20px;
    color: #666;
    margin-bottom: 50px;
    line-height: 1.8;
}

/* Action Buttons */
.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 40px;
    margin-top: 50px;
}

.button-card {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s, box-shadow 0.3s;
    border: 2px solid transparent;
}

.button-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    border-color: #28a745;
}

.button-icon {
    color: #28a745;
    margin-bottom: 20px;
}

.button-card h3 {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #2c3e50;
}

.button-card p {
    color: #666;
    margin-bottom: 20px;
    line-height: 1.6;
}

.features {
    list-style: none;
    margin-bottom: 30px;
    text-align: left;
}

.features li {
    padding: 5px 0;
    color: #555;
    font-size: 14px;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    text-decoration: none;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s;
    margin: 5px;
}

.btn-primary {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%);
    color: white;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 140, 0, 0.4);
}

/* Services Section */
.services {
    padding: 80px 0;
    background: white;
}

.services h2 {
    text-align: center;
    font-size: 36px;
    margin-bottom: 50px;
    color: #2c3e50;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.service-item {
    text-align: center;
    padding: 30px 20px;
}

.service-item h3 {
    font-size: 20px;
    margin-bottom: 15px;
    color: #2c3e50;
}

.service-item p {
    color: #666;
    line-height: 1.6;
}

/* About Section */
.about {
    padding: 80px 0;
    background: #f8f9fa;
}

.about h2 {
    text-align: center;
    font-size: 36px;
    margin-bottom: 50px;
    color: #2c3e50;
}

.about-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.about-item {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
}

.about-item h3 {
    font-size: 20px;
    margin-bottom: 15px;
    color: #2c3e50;
}

.about-item p {
    color: #666;
    line-height: 1.6;
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background: white;
    text-align: center;
}

.contact h2 {
    font-size: 36px;
    margin-bottom: 20px;
    color: #2c3e50;
}

.contact p {
    font-size: 18px;
    color: #666;
    margin-bottom: 40px;
}

.contact-info {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.contact-item {
    font-size: 16px;
    color: #555;
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    text-align: center;
    padding: 20px 0;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 30px;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    position: relative;
    max-height: 80vh;
    overflow-y: auto;
}

.close {
    position: absolute;
    right: 20px;
    top: 20px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #999;
}

.close:hover {
    color: #333;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #28a745;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 30px;
}

/* Confirmation Page Styles */
.confirmation {
    padding: 80px 0;
    background: #f8f9fa;
    min-height: 70vh;
}

.confirmation-content {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.success-card,
.error-card {
    background: white;
    padding: 50px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.success-icon,
.error-icon {
    color: #28a745;
    margin-bottom: 20px;
}

.error-icon {
    color: #dc3545;
}

.success-card h2,
.error-card h2 {
    font-size: 28px;
    margin-bottom: 20px;
    color: #2c3e50;
}

.success-message,
.error-message {
    font-size: 16px;
    margin-bottom: 30px;
    color: #666;
}

.next-steps {
    text-align: left;
    margin: 30px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

.next-steps h3 {
    margin-bottom: 15px;
    color: #2c3e50;
}

.next-steps ol {
    color: #666;
    line-height: 1.8;
}

.contact-info {
    margin: 30px 0;
    padding: 20px;
    background: #e7f3ff;
    border-radius: 10px;
}

.contact-info h3 {
    margin-bottom: 10px;
    color: #2c3e50;
}

.action-buttons {
    margin-top: 30px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .nav {
        gap: 20px;
    }
    
    .hero-content h2 {
        font-size: 36px;
    }
    
    .hero-content > p {
        font-size: 18px;
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .button-card {
        padding: 30px 20px;
    }
    
    .services h2,
    .about h2,
    .contact h2 {
        font-size: 28px;
    }
    
    .contact-info {
        flex-direction: column;
        gap: 20px;
    }
    
    .modal-content {
        margin: 10% auto;
        padding: 20px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .success-card,
    .error-card {
        padding: 30px 20px;
    }
}
